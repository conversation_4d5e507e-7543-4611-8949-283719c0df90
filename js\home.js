import gsap from "gsap";
import { ScrambleTextPlugin } from "gsap/ScrambleTextPlugin";
import { ScrollTrigger } from "gsap/ScrollTrigger";


document.addEventListener("DOMContentLoaded", () => {

  gsap.registerPlugin(ScrollTrigger);

  let scrollTriggerInstance = null;

  const initAnimations = () => {
    if (scrollTriggerInstance) {
      scrollTriggerInstance.kill();
    }

    scrollTriggerInstance = ScrollTrigger.create({
      trigger: ".hero-img-holder",
      start: "top bottom",
      end: "top top",
      onUpdate: (self) => {
        const progress = self.progress;
        gsap.set(".hero-img", {
          y: `${-110 + 110 * progress}%`,
          scale: 0.25 + 0.75 * progress,
        });
      },
    });
  };

  initAnimations();

  window.addEventListener("resize", () => {
    initAnimations();
  });
});


// SCRAMBLE TEXT 
  document.addEventListener("DOMContentLoaded", () => {
    gsap.registerPlugin(ScrambleTextPlugin);

    const fullText = [
      { el: document.querySelector(".hero h1:nth-child(1)"), text: "URGE" },
      { el: document.querySelector(".hero h1:nth-child(2)"), text: "VISIONARY" },
    ];

    fullText.forEach(({ el, text }) => {
      el.textContent = ""; // Empty first
    });

    // Animate both together like a hacker reveal
    fullText.forEach(({ el, text }) => {
      gsap.fromTo(
        el,
        { scrambleText: { text: "", chars: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", revealDelay: 0, tweenLength: false } },
        {
          scrambleText: {
            text: text,
            chars: ".#123456789-+*-~",
            speed: 1,
            revealDelay: 1,
            tweenLength: false
          },
          duration: 2.5,
          ease: "none"
        }
      );
    });
  });

// GIFS 
  document.addEventListener("DOMContentLoaded", () => {

   document.querySelectorAll(".gif").forEach((gif) => {
    gsap.set(gif, {
        y: 100,
        opacity: 0,
    });

    gsap.to(gif, {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: gif,
            start: "top bottom",
        }
    });
});
});

// FOR BUTTON REVEAL ANIMATION
  document.addEventListener("DOMContentLoaded", () => {
(function() {
  document.querySelectorAll('.btn-cta').forEach((button) => {
    // Target elements within each button
    const border = button.querySelector('.btn-cta-border');
    const title = button.querySelector('.btn-cta-title');
    
    // Initial setup for elements
    [border, title].forEach(element => {
      gsap.set(element, {
        y: 100,
        scale: element === border ? 0.1 : 1
      });
    });

    // Animation timeline for coordinated effects
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: button,
        start: "top bottom",
        markers: false
      }
    });

    // Sequence animations
    tl.to(border, {
      y: 0,
      scale: 1,
      duration: 0.9,
      ease: "expo.out"
    }).to(title, {
      y: 0,
      duration: 0.7,
      ease: "circ.inOut"
    }, "<0.2");
  });
})();
});

  document.addEventListener("DOMContentLoaded", () => {
    gsap.registerPlugin(ScrollTrigger);

    const projectWrappers = document.querySelectorAll(".project-wrapper");
  
    projectWrappers.forEach((wrapper) => {
      gsap.fromTo(
        wrapper,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: wrapper,
            start: "top bottom",
          },
        }
      );
    });
  });


  
// SCROLLER ANIMATION 
  document.addEventListener("DOMContentLoaded", () => {
gsap.to(".fixed-svg", {
    rotation: 360,
    scrollTrigger: {
        trigger: ".centered-section",
        start: "top center",
        end: "bottom top",
        scrub: true,
    }
});

document.querySelectorAll(".text-row").forEach((text) => {
    const tl = gsap.timeline({
        scrollTrigger: {
            trigger: text,
            start: "top 70%",
            end: "bottom 20%",
            scrub: true,
        }
    });

    tl.fromTo(
        text,
        { gap: "0.5rem" }, // Starting gap
        { gap: "12rem" }
    ).to(
        text,
        { gap: "0.5rem" }    // Smoothly revert to 0.5rem
    );
});
});
