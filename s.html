<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
        body{
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

      .frame {
        z-index: 997;
        pointer-events: none;
        min-width: 100%;
        min-height: 100vh;
        position: fixed;
        inset: 0%;
      }

      .frame_box {
        z-index: 1;
        pointer-events: none;
        width: 100%;
        height: 100%;
        display: block;
        position: relative;
      }

    
      .frame_border-top {
        z-index: 9;
        background-color: #070707;
        width: 100%;
        height: 0.5vw;        position: absolute;
        inset: 0% 0% auto;
      }

      .frame_border-bottom {
        z-index: 9;
        background-color: #070707;
        width: 100%;
        height: 0.5rem;        position: absolute;
        inset: auto 0% 0%;
      }

      .frame_border-left {
        z-index: 9;
        background-color: #070707;
        justify-content: flex-start;
        align-items: stretch;
           width: 0.5vw;
        height: 100%;
        display: block;
        position: absolute;
        inset: 0% auto 0% 0%;
      }

      .frame_border-right {
        z-index: 9;
        background-color: #070707;
                 width: 0.5vw;
       height: 100%;
        display: block;
        position: absolute;
        inset: 0% 0% 0% auto;
      }

      .spacerone {
        width: 100vw;
        height: 100vh;
        background-color: blue;
      }

      .spacertwo {
        width: 100vw;
        height: 100vh;
        background-color: rgb(0, 162, 255);
      }
    </style>
  </head>
  <body>
    <div class="frame">
      <div class="frame_box">
        <div class="frame_border-top"></div>
        <div class="frame_border-bottom"></div>
        <div class="frame_border-left"></div>
        <div class="frame_border-right"></div>
      </div>
   
   
    </div>

    <div class="spacerone"></div>
    <div class="spacertwo"></div>
  </body>
</html>
