import * as THREE from "three";
import { heroVertexShader, heroFragmentShader } from "../shader/shaders.js";

class HeroBurnAnimation {
  constructor() {
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.uniforms = null;
    this.animationId = null;
    this.isAnimating = false;
    this.startTime = 0;
    this.duration = 3000; // 3 seconds animation duration
    
    this.config = {
      burnIntensity: 0.15,
      burnColor: [1.0, 0.4, 0.1], // Orange burn color
      emberColor: [1.0, 0.8, 0.2], // Yellow ember color
    };

    this.init();
  }

  init() {
    const heroContainer = document.querySelector('.hero-img');
    const heroImage = heroContainer.querySelector('img');
    
    if (!heroContainer || !heroImage) {
      console.error('Hero image container or image not found');
      return;
    }

    // Wait for image to load
    if (heroImage.complete) {
      this.setupScene(heroContainer, heroImage);
    } else {
      heroImage.addEventListener('load', () => {
        this.setupScene(heroContainer, heroImage);
      });
    }
  }

  setupScene(container, img) {
    const loader = new THREE.TextureLoader();
    
    loader.load(img.src, (texture) => {
      // Create scene
      this.scene = new THREE.Scene();
      
      // Get container dimensions
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      // Create camera
      this.camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
      
      // Setup texture
      texture.minFilter = THREE.LinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.wrapS = THREE.ClampToEdgeWrapping;
      texture.wrapT = THREE.ClampToEdgeWrapping;
      
      // Create uniforms
      this.uniforms = {
        u_texture: { value: texture },
        u_time: { value: 0.0 },
        u_resolution: { value: new THREE.Vector2(width, height) },
        u_progress: { value: 0.0 },
        u_burnIntensity: { value: this.config.burnIntensity },
        u_burnColor: { value: new THREE.Vector3(...this.config.burnColor) },
        u_emberColor: { value: new THREE.Vector3(...this.config.emberColor) }
      };
      
      // Create geometry and material
      const geometry = new THREE.PlaneGeometry(2, 2);
      const material = new THREE.ShaderMaterial({
        uniforms: this.uniforms,
        vertexShader: heroVertexShader,
        fragmentShader: heroFragmentShader,
        transparent: true
      });
      
      // Create mesh
      const mesh = new THREE.Mesh(geometry, material);
      this.scene.add(mesh);
      
      // Create renderer
      this.renderer = new THREE.WebGLRenderer({
        alpha: true,
        antialias: true,
        powerPreference: "high-performance"
      });
      this.renderer.setSize(width, height);
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      
      // Create canvas container
      const canvasContainer = document.createElement('div');
      canvasContainer.className = 'hero-burn-canvas';
      canvasContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        pointer-events: none;
      `;
      
      canvasContainer.appendChild(this.renderer.domElement);
      container.appendChild(canvasContainer);
      
      // Hide original image initially
      img.style.opacity = '0';
      
      // Setup intersection observer to trigger animation
      this.setupIntersectionObserver(container);
      
      // Handle resize
      this.setupResizeHandler(container);
    });
  }

  setupIntersectionObserver(container) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !this.isAnimating) {
            this.startBurnAnimation();
          }
        });
      },
      { threshold: 0.3 }
    );
    
    observer.observe(container);
  }

  setupResizeHandler(container) {
    const resizeObserver = new ResizeObserver(() => {
      if (this.renderer && this.uniforms) {
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        this.renderer.setSize(width, height);
        this.uniforms.u_resolution.value.set(width, height);
      }
    });
    
    resizeObserver.observe(container);
  }

  startBurnAnimation() {
    if (this.isAnimating) return;
    
    this.isAnimating = true;
    this.startTime = Date.now();
    this.animate();
  }

  animate() {
    const currentTime = Date.now();
    const elapsed = currentTime - this.startTime;
    const progress = Math.min(elapsed / this.duration, 1);
    
    // Easing function for smooth animation
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    
    if (this.uniforms) {
      this.uniforms.u_time.value = currentTime * 0.001;
      this.uniforms.u_progress.value = easeOutCubic;
    }
    
    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
    }
    
    if (progress < 1) {
      this.animationId = requestAnimationFrame(() => this.animate());
    } else {
      this.isAnimating = false;
      // Show original image after animation completes
      const heroImage = document.querySelector('.hero-img img');
      if (heroImage) {
        heroImage.style.opacity = '1';
        heroImage.style.transition = 'opacity 0.5s ease';
      }
      // Hide canvas after a delay
      setTimeout(() => {
        const canvas = document.querySelector('.hero-burn-canvas');
        if (canvas) {
          canvas.style.opacity = '0';
          canvas.style.transition = 'opacity 0.5s ease';
        }
      }, 500);
    }
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    if (this.scene) {
      this.scene.clear();
    }
  }
}

// Initialize the hero burn animation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new HeroBurnAnimation();
});

export default HeroBurnAnimation;
